@echo off
chcp 65001 >nul
echo ===============================================
echo    📋 اختبار زر التكليف - النسخة المبسطة
echo ===============================================
echo.

echo 🧹 تنظيف المشروع...
dotnet clean

echo 🔨 بناء المشروع...
dotnet build --configuration Debug

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.

echo 🚀 تشغيل التطبيق...
start "" "bin\Debug\net9.0-windows\SFDSystem.exe"

echo.
echo 📝 تعليمات الاختبار:
echo 1. اذهب إلى قسم "التقارير"
echo 2. اختر أي زيارة من القائمة
echo 3. اضغط على زر "التكليف" 📋
echo 4. يجب أن تفتح النافذة المبسطة بدون أخطاء
echo.
echo ✅ النافذة المبسطة تحتوي على:
echo - بيانات ثابتة (لا تحتاج قاعدة بيانات)
echo - واجهة جميلة ومهنية
echo - طباعة مباشرة
echo.
echo 🔧 إذا ظهر خطأ:
echo - اضغط على زر "استعادة" 🗄️ في الشريط الجانبي
echo - اختر "استعادة البيانات" أو "إعادة تعيين كاملة"
echo.

pause
