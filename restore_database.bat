@echo off
chcp 65001 >nul
echo ===============================================
echo    🗄️ أداة استعادة قاعدة البيانات السريعة
echo ===============================================
echo.

echo 📊 فحص قاعدة البيانات الحالية...
if exist "Data\SFDSYS.db" (
    echo ✅ تم العثور على قاعدة البيانات
    for %%A in ("Data\SFDSYS.db") do echo 📏 حجم الملف: %%~zA بايت
) else (
    echo ❌ لم يتم العثور على قاعدة البيانات
)

echo.
echo 🔧 الخيارات المتاحة:
echo 1. تشغيل التطبيق مع نافذة استعادة قاعدة البيانات
echo 2. إعادة بناء المشروع
echo 3. تشغيل التطبيق العادي
echo 4. إنهاء
echo.

set /p choice="اختر رقم الخيار (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🚀 تشغيل التطبيق مع نافذة الاستعادة...
    start "" "bin\Debug\net9.0-windows\SFDSystem.exe"
    echo ✅ تم تشغيل التطبيق
    echo 💡 اذهب إلى الشريط الجانبي واضغط على زر "استعادة" 🗄️
) else if "%choice%"=="2" (
    echo.
    echo 🔨 إعادة بناء المشروع...
    dotnet build --configuration Debug
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم بناء المشروع بنجاح
        echo 🚀 تشغيل التطبيق...
        start "" "bin\Debug\net9.0-windows\SFDSystem.exe"
    ) else (
        echo ❌ فشل في بناء المشروع
    )
) else if "%choice%"=="3" (
    echo.
    echo 🚀 تشغيل التطبيق العادي...
    start "" "bin\Debug\net9.0-windows\SFDSystem.exe"
    echo ✅ تم تشغيل التطبيق
) else if "%choice%"=="4" (
    echo.
    echo 👋 إنهاء البرنامج...
    exit /b 0
) else (
    echo.
    echo ❌ خيار غير صحيح
)

echo.
echo 📝 ملاحظات مهمة:
echo - إذا كانت البيانات فارغة، استخدم زر "استعادة البيانات" في النافذة
echo - إذا كانت هناك مشاكل، استخدم "إعادة تعيين كاملة" (سيحذف البيانات الحالية)
echo - تأكد من إغلاق التطبيق قبل إعادة البناء
echo.

pause
