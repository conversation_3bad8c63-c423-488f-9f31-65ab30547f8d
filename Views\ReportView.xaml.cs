using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    public partial class ReportView : UserControl
    {
        public ReportView()
        {
            InitializeComponent();
            // إنشاء ViewModel افتراضي فقط إذا لم يكن هناك DataContext
            if (DataContext == null)
            {
                DataContext = new ReportViewModel();
            }
        }

        /// <summary>
        /// Constructor مع DataContext محدد مسبقاً
        /// </summary>
        public ReportView(ReportViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }

        /// <summary>
        /// طباعة التقرير مباشرة
        /// </summary>
        private void PrintReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DriverManagementSystem.Services.ReportViewPrintService.PrintReportView(this, "تقرير الزيارة الميدانية");
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        private void PrintPreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DriverManagementSystem.Services.ReportViewPrintService.PrintReportView(this);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحرير قالب العقد
        /// </summary>
        private void EditContractButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var contractEditorWindow = new DriverManagementSystem.Views.ContractEditorWindow();
                contractEditorWindow.ShowDialog();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تحرير العقد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// توثيق الرسائل النصية
        /// </summary>
        private void MessageDocumentationButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الزيارة المحددة من ViewModel
                var viewModel = DataContext as ReportViewModel;
                var selectedVisit = viewModel?.SelectedVisit;

                if (selectedVisit != null)
                {
                    // فتح نافذة توثيق الرسائل مع الزيارة المحددة
                    var messageWindow = new DriverManagementSystem.Views.PowerfulMessageDocumentationWindow(selectedVisit);
                    messageWindow.ShowDialog();
                }
                else
                {
                    // فتح نافذة توثيق الرسائل العامة
                    var messageWindow = new DriverManagementSystem.Views.PowerfulMessageDocumentationWindow();
                    messageWindow.ShowDialog();
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة توثيق الرسائل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// التكليف
        /// </summary>
        private void AssignmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الزيارة المحددة من ViewModel
                var viewModel = DataContext as ReportViewModel;
                var selectedVisit = viewModel?.SelectedVisit;

                if (selectedVisit != null)
                {
                    // فتح نافذة التكليف المبسطة مع الزيارة المحددة
                    var assignmentWindow = new DriverManagementSystem.Views.SimpleAssignmentWindow(selectedVisit);
                    assignmentWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار زيارة أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة التكليف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
